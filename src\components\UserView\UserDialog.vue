<script setup lang="ts">
import { ref, useTemplateRef, watch, computed } from 'vue';
import { ElMessage, type FormRules } from 'element-plus';
import { AdminService } from '@/api';
import type { MomUser, FormUser, MomRole, MomOrganization } from '../../../../xtrade-sdk/dist';

const { user } = defineProps<{
  user?: MomUser;
}>();

const visible = defineModel<boolean>();

const emit = defineEmits<{
  success: [];
}>();

// 表单校验规则
const rules: FormRules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  fullName: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 8, max: 12, message: '长度8-12位', trigger: 'blur' },
    {
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^]{8,}$/,
      message: '数字、大小写字母组合',
      trigger: 'blur',
    },
  ],
  email: [
    {
      required: true,
      message: '请输入邮箱',
      trigger: 'blur',
    },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
  ],
  phoneNo: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur',
    },
  ],
  orgId: [{ required: true, message: '请选择机构', trigger: 'change' }],
  roleId: [{ required: true, message: '请选择角色', trigger: 'change' }],
};

const formRef = useTemplateRef('formRef');

const form = ref<FormUser>({
  email: '',
  username: '',
  fullName: '',
  password: '',
  phoneNo: '',
  orgId: undefined,
  orgName: '',
  roleId: undefined,
  roleName: '',
  status: 1,
  ip: '',
  mac: '',
  userType: 0,
});

// 角色列表
const roles = ref<MomRole[]>([]);
// 机构列表
const orgs = ref<MomOrganization[]>([]);

// 是否编辑模式
const isEdit = computed(() => !!user);

// 对话框标题
const dialogTitle = computed(() => (isEdit.value ? '编辑用户信息' : '创建用户'));

// 监听visible变化
watch(visible, async val => {
  if (val) {
    // 加载角色和机构列表
    await loadRolesAndOrgs();

    if (user) {
      // 编辑模式，填充表单数据
      form.value = {
        username: user.username,
        fullName: user.fullName,
        password: '',
        email: user.email,
        phoneNo: user.phoneNo || '',
        orgId: user.orgId,
        orgName: user.orgName,
        roleId: user.roleId,
        status: user.status,
        ip: user.ip || '',
        mac: user.mac || '',
        userType: user.userType,
      };
    } else {
      // 新建模式，重置表单
      resetForm();
    }
  }
});

// 加载角色和机构列表
const loadRolesAndOrgs = async () => {
  try {
    const [rolesData, orgsData] = await Promise.all([
      AdminService.getRoles(),
      AdminService.getOrgs(),
    ]);
    roles.value = rolesData;
    orgs.value = orgsData;
  } catch (error) {
    console.error('加载角色和机构列表失败:', error);
    ElMessage.error('加载数据失败');
  }
};

// 重置表单
const resetForm = () => {
  form.value = {
    username: '',
    fullName: '',
    password: '',
    email: '',
    phoneNo: '',
    orgId: undefined,
    roleId: undefined,
    status: 1,
    ip: '',
    mac: '',
    userType: 0,
  };
  formRef.value?.clearValidate();
};

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async valid => {
    if (valid) {
      try {
        if (isEdit.value && user) {
          // 编辑用户
          const {
            // password,
            ...updateData
          } = {
            ...user,
            ...form.value,
          };
          const { errorCode, errorMsg } = await AdminService.updateUser(updateData);
          if (errorCode === 0) {
            emit('success');
            ElMessage.success('修改成功');
            handleClose();
          } else {
            ElMessage.error(errorMsg || '操作失败');
          }
        } else {
          // 创建用户
          const { errorCode, errorMsg } = await AdminService.createUser({
            ...form.value,
            // password: Utils.aesEncrypt(form.value.password!),
          });
          if (errorCode === 0) {
            emit('success');
            ElMessage.success('添加成功');
            handleClose();
          } else {
            ElMessage.error(errorMsg || '操作失败');
          }
        }
      } catch (error) {
        console.error('操作失败:', error);
        ElMessage.error('操作失败');
      }
    }
  });
};

// 关闭对话框
const handleClose = () => {
  visible.value = false;
  resetForm();
};

const handleRoleChange = (roleId: number) => {
  const role = roles.value.find(role => role.id === roleId);
  if (role) {
    form.value.roleName = role.roleName;
  }
};

const handleOrgChange = (orgId: number) => {
  const org = orgs.value.find(org => org.id === orgId);
  if (org) {
    form.value.orgName = org.orgName;
  }
};
</script>

<template>
  <el-dialog
    :model-value="visible"
    :title="dialogTitle"
    width="600px"
    class="typical-form"
    label-position="left"
    label-width="110px"
    @close="handleClose"
    draggable
  >
    <el-form ref="formRef" :model="form" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" prop="username">
            <el-input v-model.trim="form.username" placeholder="请输入用户名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="真实姓名" prop="fullName">
            <el-input v-model.trim="form.fullName" placeholder="请输入真实姓名" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="!isEdit">
        <el-col :span="12">
          <el-form-item label="密码" prop="password">
            <el-input
              v-model.trim="form.password"
              type="password"
              placeholder="请输入密码"
              show-password
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model.trim="form.email" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="isEdit">
        <el-col :span="24">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model.trim="form.email" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="电话号码" prop="phoneNo">
            <el-input v-model.trim="form.phoneNo" placeholder="请输入电话号码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="机构" prop="orgId">
            <el-select
              v-model="form.orgId"
              placeholder="请选择机构"
              w-full
              @change="handleOrgChange"
            >
              <el-option v-for="org in orgs" :key="org.id" :label="org.orgName" :value="org.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="角色" prop="roleId">
            <el-select
              v-model="form.roleId"
              placeholder="请选择角色"
              w-full
              @change="handleRoleChange"
            >
              <el-option
                v-for="role in roles"
                :key="role.id"
                :label="role.roleName"
                :value="role.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态">
            <el-select v-model="form.status" w-full>
              <el-option label="正常" :value="1" />
              <el-option label="禁用" :value="0" />
              <el-option label="冻结" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="允许IP">
            <el-input v-model.trim="form.ip" placeholder="允许的IP地址" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="允许MAC">
            <el-input v-model.trim="form.mac" placeholder="允许的MAC地址" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<style scoped></style>
