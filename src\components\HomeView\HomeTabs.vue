<script setup lang="ts">
import type { TabPaneName } from 'element-plus';
import { computed } from 'vue';
import type { MomMenuTree } from '../../../../xtrade-sdk/dist';

const { tabs, activeMenu } = defineProps<{
  tabs: MomMenuTree[];
  activeMenu: MomMenuTree | null;
}>();

const emit = defineEmits<{
  clickMenu: [item: MomMenuTree];
  closeTab: [item: MomMenuTree];
}>();

const activeName = computed({
  get() {
    if (!activeMenu) {
      return '';
    }
    return activeMenu.menuRoute;
  },
  set(value) {
    emit('clickMenu', tabs.find(tab => tab.menuRoute === value)!);
  },
});

const handleCloseTab = (tabName: TabPaneName) => {
  emit('closeTab', tabs.find(tab => tab.menuRoute === tabName)!);
};
</script>

<template>
  <div class="home-tabs" h-full w-full>
    <el-tabs
      type="card"
      :editable="tabs.length > 1"
      :closable="false"
      v-model="activeName"
      @tab-remove.stop="handleCloseTab"
    >
      <el-tab-pane
        v-for="tab in tabs"
        :key="tab.menuRoute"
        :label="tab.menuName"
        :name="tab.menuRoute"
      >
        <template #label>
          <span class="custom-tabs-label" flex aic gap-12>
            <span>{{ tab.menuName }}</span>
            <i class="iconfont icon-close" @click.stop="handleCloseTab(tab.menuRoute!)" />
          </span>
        </template>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style scoped>
.home-tabs {
  :deep() {
    .el-tabs {
      background-color: unset !important;
      --el-transition-duration: 0.1s;
    }

    .el-icon {
      display: none;
    }

    .el-tabs__item {
      .iconfont {
        visibility: hidden;
      }
      &:hover {
        .iconfont {
          visibility: visible;
        }
      }
    }

    .el-tabs__header,
    .el-tabs__nav-wrap,
    .el-tabs__nav-scroll,
    .el-tabs__nav,
    .el-tabs__item {
      height: 36px !important;
    }

    .el-tabs__nav-next,
    .el-tabs__nav-prev {
      line-height: 32px;
    }

    .el-tabs__nav {
      gap: 8px;
      background-color: var(--g-bg);
      border: unset;

      .el-tabs__item {
        padding: 9px 10px 9px 20px !important;
        background-color: var(--g-panel-bg2);
        border-radius: 4px !important;
        font-size: 14px !important;
        font-weight: 400 !important;
        color: var(--g-panel-text) !important;

        &:hover {
          background-color: var(--g-bg-hover-4) !important;
          color: var(--g-text-color-2) !important;
        }

        &.is-active {
          background-color: var(--g-active) !important;
          color: var(--g-text-color-2) !important;
        }
      }
    }
  }
}
</style>
