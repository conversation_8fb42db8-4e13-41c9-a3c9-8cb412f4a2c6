html {
  margin: 0;
  width: 100%;
  height: 100%;
  background-color: var(--g-bg);

  body {
    margin: 0;
    width: 100%;
    height: 100%;
    color: var(--g-white);
    font-size: 12px;

    * {
      box-sizing: border-box;
    }

    #app {
      margin: 0;
      width: 100%;
      height: 100%;
    }

    /* element ui */
    --el-color-primary: var(--g-primary);
    --el-text-color-placeholder: var(--g-text-color-7);
    --el-text-color-regular: white;
    --el-bg-color-overlay: var(--g-bg);
    --el-border-color: var(--g-bg);
    --el-border-color-hover: var(--g-bg-hover-1);
    --el-border-radius-base: 8px;
    --el-font-size-base: 14px;
    --el-component-size-large: 44px;

    .el-button {
      --el-button-bg-color: var(--g-bg-hover-5);
      --el-button-border-color: var(--g-bg-hover-5);
    }

    .el-button--default {
      --el-button-text-color: var(--el-color-white);
      --el-button-bg-color: var(--g-block-bg-2);
    }

    .el-button--primary {
      --el-button-text-color: var(--el-color-white);
      --el-button-bg-color: var(--g-block-bg-8);
      --el-button-border-color: var(--g-block-bg-8);
      --el-button-hover-bg-color: var(--g-block-bg-9);
    }

    .el-scrollbar {
      /* --el-scrollbar-opacity: 0.7; */
      .el-scrollbar__thumb {
        background-color: rgb(124, 152, 187);
        &:hover {
          opacity: 0.9;
        }
      }
    }

    .el-message-box {
      --el-messagebox-width: 320px;
      --el-messagebox-padding-primary: 20px;
      .el-message-box__btns {
        flex-direction: column-reverse;
        gap: 20px;
        padding-top: 20px;
        .el-button {
          width: 100%;
          &.delete {
            background-color: var(--g-bg-red);
            border-color: var(--g-bg-red);
            &:hover {
              background-color: var(--g-bg-red-hover);
              border-color: var(--g-bg-red-hover);
            }
          }
          & + .el-button {
            margin-left: 0;
          }
        }
      }
    }

    .el-menu {
      border-right: unset;
      --el-menu-bg-color: var(--g-bg-1);
      --el-menu-border-color: var(--g-bg-1);
      --el-menu-text-color: var(--g-panel-text);
      --el-menu-hover-bg-color: var(--g-hover);
      --el-menu-item-height: 36px;
      --el-menu-sub-item-height: 36px;
      --el-menu-item-font-size: 14px;

      .el-menu-item {
        &:hover {
          color: white;
        }

        &.is-active {
          color: white;
          /* background-color: var(--g-primary); */
        }
      }

      .el-sub-menu {
        .el-sub-menu__title {
          &:hover {
            color: white;
          }
        }
      }
    }

    .el-tabs {
      --el-tabs-header-height: 32px;
      padding-left: 12px;

      .el-tabs__header {
        margin-bottom: 0;
        border-bottom: none;

        .el-tabs__nav-wrap {
          margin-bottom: 0;
          .el-tabs__nav-scroll {
            .el-tabs__nav {
              border: none;
              border-radius: 0;
              .el-tabs__item {
                border: none;
                background-color: var(--g-block-bg-2) !important;
                color: var(--g-text-color-1) !important;
                height: 30px;
                border-top-right-radius: 2px;
                border-top-left-radius: 2px;
                padding-left: 10px;
                padding-right: 10px;
                font-size: 14px;
                &:hover {
                  background-color: var(--g-block-bg-6) !important;
                  color: var(--g-text-color-2) !important;
                  .is-icon-close {
                    opacity: 1;
                  }
                }
                &.is-active {
                  color: var(--g-text-color-2) !important;
                  background-color: var(--g-block-bg-11) !important;
                }
                .is-icon-close {
                  width: 16px;
                  height: 16px;
                  opacity: 0;

                  svg {
                    width: 100% !important;
                    height: 100% !important;
                  }
                }
              }
            }
          }
        }
        .el-tabs__new-tab {
          display: none;
        }
      }
    }

    .el-select {
      /* --el-select-font-size: 12px; */
      /* --el-select-input-font-size: 12px; */
      --el-select-border-color-hover: transparent;
      --el-select-disabled-border: transparent;
      --el-select-input-color: white;
      --el-select-multiple-input-color: white;
      --el-select-close-hover-color: white;

      .el-select__wrapper {
        /* font-size: 12px; */
        background-color: var(--g-block-bg-2);
        line-height: 28px;
        min-height: 28px;

        /* box-shadow: var(--g-input-shadow);
        &.is-hovering {
          &:not(.is-focused) {
            box-shadow: var(--g-input-shadow);
          }
        }
        &.is-focused {
          box-shadow: 0 0 0 1px var(--el-color-primary);
        } */
        /* .el-select__selection {
        } */
        /* .el-select__suffix {
        } */
      }
    }

    .el-select-dropdown {
      .el-select-dropdown__item {
        /* font-size: 12px; */
        &.is-selected,
        &.is-hovering {
          background-color: var(--g-hover);
        }
      }
    }

    .el-popper {
      &.is-light {
        border: none;
      }
      &[data-popper-placement='bottom-start'] {
        .el-popper__arrow {
          &::before {
            border-top-color: transparent;
            border-left-color: transparent;
          }
        }
      }
    }

    .el-form {
      .el-form-item {
        .el-form-item__label {
          color: var(--g-text-color-7);
          &::before {
            vertical-align: middle;
          }
        }
      }
    }

    .el-input {
      .el-input__wrapper {
        background-color: var(--g-block-bg-10);
      }
    }

    .el-checkbox__inner {
      --el-checkbox-input-border: 1.2px solid #8a8a98;
      --el-checkbox-input-width: 16px;
      --el-checkbox-input-height: 16px;

      &:hover {
        border-color: var(--g-white);
      }
    }

    .el-checkbox__input {
      &.is-checked {
        .el-checkbox__inner {
          --el-checkbox-checked-bg-color: #384cff;
          border-color: #384cff !important;
          &:after {
            --el-checkbox-checked-icon-color: #000;
          }
        }
      }
    }

    .el-textarea {
      background-color: var(--g-block-bg-10);
    }

    .el-input-number {
      --el-fill-color-light: var(--g-panel-bg3);
      .el-input-number__decrease {
        border-right-color: var(--g-bg);
      }
      .el-input-number__increase {
        border-left-color: var(--g-bg);
      }
      .el-input-number__decrease,
      .el-input-number__increase {
        &.is-disabled {
          background-color: var(--g-panel-bg);
        }
      }
    }

    .el-checkbox {
      --el-checkbox-font-weight: 400;
      --el-checkbox-input-border: 1px solid var(--g-border);
    }

    .el-table-v2 {
      --el-table-border-color: transparent;
      --el-table-header-bg-color: var(--g-bg);
      --el-table-header-text-color: var(--g-text-color-1);
      --el-table-row-hover-bg-color: var(--g-hover-row);
      .el-table-v2__table {
        background-color: var(--g-bg);
        .el-table-v2__header-wrapper {
          background-color: var(--g-bg);
          .el-table-v2__header {
            .el-table-v2__header-row {
              .el-table-v2__header-cell {
                .el-table-v2__sort-icon {
                  line-height: unset;
                }
              }
            }
          }
        }
        .el-table-v2__body {
          .el-table-v2__row {
            color: white;
          }
        }
      }
    }

    .el-table-v2__row.is-hovered,
    .el-table-v2__row:hover {
      --el-table-row-hover-bg-color: #2c2c35;
    }

    .el-dialog {
      --el-dialog-border-radius: 12px;
      --el-dialog-bg-color: #141414;
      .el-dialog__footer {
        text-align: center;
        display: flex;
        justify-content: center;
        .el-button {
          min-width: 100px;
        }
      }
    }

    .el-overlay {
      --el-overlay-color-lighter: rgba(78, 78, 78, 0.6);
    }
  }

  .el-scrollbar__bar {
    &.is-vertical {
      width: 10px;
    }
    &.is-horizontal {
      height: 10px;
    }
  }

  .el-virtual-scrollbar {
    &.el-vl__vertical {
      width: 10px !important;
    }
    &.el-vl__horizontal {
      height: 10px !important;
    }
  }
}

/*滚动条样式*/
::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 5px;
  height: 5px;
  position: absolute;
}

::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  background: transparent;
}

::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  background: #b6b9dd;
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-corner {
  background: transparent;
  border: none;
}
