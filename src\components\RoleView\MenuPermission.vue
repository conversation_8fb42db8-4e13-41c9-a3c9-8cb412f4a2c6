<script setup lang="tsx">
import { watch } from 'vue';
import type { RoleMenuPermissionTree, RoleMomPermission } from '@/types';
import type { MomMenuTree } from '../../../../xtrade-sdk/dist';

const { fullMenus, roleMenus, initStatusCount } = defineProps<{
  fullMenus: RoleMenuPermissionTree[];
  roleMenus: MomMenuTree[];
  initStatusCount: number;
}>();

const getChileRoleMenus = (menu: MomMenuTree) => {
  return roleMenus.find(item => item.id === menu.id)?.children || [];
};

const getMenuPermissionTypes = (menu: RoleMenuPermissionTree) => {
  const permissions = menu.menListPermission || [];
  // 将permissions按type分组
  const types: Record<string, RoleMomPermission[]> = {};
  permissions.forEach(permission => {
    if (!types[permission.type]) {
      types[permission.type] = [];
    }
    types[permission.type].push(permission);
  });
  return types;
};

// 初始化时根据roleMenus设置checked状态
const initializeCheckedStatus = () => {
  const setCheckedStatus = (menus: RoleMenuPermissionTree[], roleMenus: MomMenuTree[]) => {
    menus.forEach(menu => {
      const roleMenu = roleMenus.find(rm => rm.id === menu.id);

      // 重置菜单状态
      menu.checked = !!roleMenu;

      // 重置权限状态
      if (menu.menListPermission) {
        menu.menListPermission.forEach(permission => {
          const rolePermission = roleMenu?.menListPermission?.find(rp => rp.id === permission.id);
          permission.checked = !!rolePermission;
        });
      }

      // 递归处理子菜单
      if (menu.children) {
        setCheckedStatus(menu.children, roleMenu?.children || []);
      }
    });
  };

  setCheckedStatus(fullMenus, roleMenus);
};

// 监听roleMenus变化，重新初始化checked状态
watch(
  () => initStatusCount,
  () => {
    initializeCheckedStatus();
  },
  { immediate: true, deep: true },
);

// 检查菜单是否应该为中间状态
const getMenuIndeterminate = (menu: RoleMenuPermissionTree): boolean => {
  console.log(menu.menuName);

  if (!menu.children && !menu.menListPermission) return false;

  let checkedCount = 0;
  let totalCount = 0;

  // 检查子菜单
  if (menu.children) {
    menu.children.forEach(child => {
      totalCount++;
      if (child.checked) checkedCount++;
    });
  }

  // 检查权限
  if (menu.menListPermission) {
    menu.menListPermission.forEach(permission => {
      totalCount++;
      if (permission.checked) checkedCount++;
    });
  }

  return checkedCount > 0 && checkedCount < totalCount;
};

// 处理菜单checkbox变化
const handleMenuChange = (menu: RoleMenuPermissionTree, checked: boolean) => {
  menu.checked = checked;

  // 设置所有子菜单的状态
  if (menu.children) {
    menu.children.forEach(child => {
      handleMenuChange(child, checked);
    });
  }

  // 设置所有权限的状态
  if (menu.menListPermission) {
    menu.menListPermission.forEach(permission => {
      permission.checked = checked;
    });
  }

  // 更新父级菜单状态
  // updateParentMenuStatus();
};

// 处理权限checkbox变化
const handlePermissionChange = () => {
  // 更新父级菜单状态
  // updateParentMenuStatus();
};
</script>
<template>
  <div flex-col flex gap-16 flex-1 min-w-1>
    <div v-for="menu in fullMenus" :key="menu.menuName" flex gap-16>
      <div h-31 flex aic gap-8>
        <el-checkbox
          :model-value="menu.checked"
          :indeterminate="getMenuIndeterminate(menu)"
          @change="checked => handleMenuChange(menu, !!checked)"
        />
        <div flex-1 min-w-1 cursor-pointer @click="handleMenuChange(menu, !menu.checked)">
          {{ menu.menuName }}
        </div>
      </div>
      <!-- 非叶子节点 -->
      <menu-permission
        v-if="menu.children"
        :full-menus="menu.children"
        :role-menus="getChileRoleMenus(menu)"
        :init-status-count="initStatusCount"
      ></menu-permission>
      <!-- 叶子节点菜单 -->
      <div v-else flex flex-col gap-16 flex-1 min-w-1>
        <div v-for="(permissions, type) in getMenuPermissionTypes(menu)" :key="type" flex gap-16>
          <div h-31 flex aic gap-16>{{ type }}：</div>
          <div flex-1 min-w-1 flex flex-wrap gap-16>
            <div v-for="permission in permissions" :key="permission.id">
              <div h-31 flex aic gap-8>
                <el-checkbox
                  :model-value="permission.checked"
                  @change="
                    checked => {
                      permission.checked = !!checked;
                    }
                  "
                />
                <div cursor-pointer @click="permission.checked = !permission.checked">
                  {{ permission.permissionZhName }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
