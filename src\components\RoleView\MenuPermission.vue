<script setup lang="tsx">
import type { RoleMenuPermissionTree, RoleMomPermission } from '@/types';
import type { MomMenuTree } from '../../../../xtrade-sdk/dist';
const { fullMenus, roleMenus } = defineProps<{
  fullMenus: RoleMenuPermissionTree[];
  roleMenus: MomMenuTree[];
}>();

const getChileRoleMenus = (menu: MomMenuTree) => {
  return roleMenus.find(item => item.id === menu.id)?.children || [];
};

const getMenuPermissionTypes = (menu: RoleMenuPermissionTree) => {
  const permissions = menu.menListPermission || [];
  // 将permissions按type分组
  const types: Record<string, RoleMomPermission[]> = {};
  permissions.forEach(permission => {
    if (!types[permission.type]) {
      types[permission.type] = [];
    }
    types[permission.type].push(permission);
  });
  return types;
};
</script>
<template>
  <div flex-col flex gap-16 flex-1 min-w-1>
    <div v-for="menu in fullMenus" :key="menu.menuName" flex gap-16>
      <div h-31 flex aic gap-16>
        <el-checkbox v-model="menu.checked" />
        <div flex-1 min-w-1 cursor-pointer @click="menu.checked = !menu.checked">
          {{ menu.menuName }}
        </div>
      </div>
      <!-- 非叶子节点 -->
      <menu-permission
        v-if="menu.children"
        :full-menus="menu.children"
        :role-menus="getChileRoleMenus(menu)"
      ></menu-permission>
      <!-- 叶子节点菜单 -->
      <div v-else flex flex-col gap-16 flex-1 min-w-1>
        <div v-for="(permissions, type) in getMenuPermissionTypes(menu)" :key="type" flex gap-16>
          <div h-31 flex aic gap-16>{{ type }}：</div>
          <div flex-1 min-w-1 flex flex-wrap gap-16>
            <div v-for="permission in permissions" :key="permission.id">
              <div h-31 flex aic gap-16>
                <el-checkbox v-model="permission.checked" />
                <div cursor-pointer @click="permission.checked = !permission.checked">
                  {{ permission.permissionZhName }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
