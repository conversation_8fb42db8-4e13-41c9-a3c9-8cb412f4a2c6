<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import AccountBasicInfoForm from './AccountBasicInfoForm.vue';
import AccountEquityForm from './AccountEquityForm.vue';
import AccountFeeForm from './AccountFeeForm.vue';
import { onMounted, reactive, ref, useTemplateRef, nextTick, inject } from 'vue';
import { ElMessage, ElMessageBox, TableV2SortOrder } from 'element-plus';
import { getEmptyTableColumnConfig, type ColumnDefinition, type RowAction } from '@/types';
import { Formatter, Utils } from '@/script';
import { TABLE_COLUMN_SELECT_KEY } from '@/keys';

import {
  AssetType,
  Repos,
  type AccountEquityInfo,
  type LegacyAccountInfo,
} from '../../../../xtrade-sdk/dist';

interface CellRenderParam {
  rowData: LegacyAccountInfo;
  cellData: any;
}

const repoInstance = new Repos.GovernanceRepo();
const AssetTypes = Object.values(AssetType);
// const aa = {} as any as LegacyAccountInfo;
// console.log(aa.assetType);

// 基础列定义
const columns: ColumnDefinition<LegacyAccountInfo> = [
  { key: 'accountName', title: '账号', width: 190, sortable: true, cellRenderer: renderNameCol },
  { key: 'balance', title: '权益', width: 130, sortable: true, cellRenderer: render2Thousands },
  { key: 'marketValue', title: '市值', width: 130, sortable: true, cellRenderer: render2Thousands },
  {
    key: 'available',
    title: '可用资金',
    width: 130,
    sortable: true,
    cellRenderer: render2Thousands,
  },
  {
    key: 'profitRatio',
    title: '收益率',
    width: 100,
    sortable: true,
    cellRenderer: renderPercentage,
  },
  {
    key: 'positionProfit',
    title: '持仓盈亏',
    width: 100,
    sortable: true,
    cellRenderer: render2Thousands,
  },
  { key: 'nav', title: '净值', width: 100, sortable: true, cellRenderer: render2Thousands },
  {
    key: 'preBalance',
    title: '昨日权益',
    width: 100,
    sortable: true,
    cellRenderer: render2Thousands,
  },
  {
    key: 'frozenMargin',
    title: '冻结资金',
    width: 100,
    sortable: true,
    cellRenderer: render2Thousands,
  },
  {
    key: 'closeProfit',
    title: '平仓盈亏',
    width: 100,
    sortable: true,
    cellRenderer: render2Thousands,
  },
  {
    key: 'loanBuyBalance',
    title: '融资买入',
    width: 100,
    sortable: true,
    cellRenderer: render2Thousands,
  },
  {
    key: 'loanSellBalance',
    title: '融券卖出',
    width: 100,
    sortable: true,
    cellRenderer: render2Thousands,
  },
  {
    key: 'commission',
    title: '手续费',
    width: 100,
    sortable: true,
    cellRenderer: render2Thousands,
  },
  {
    key: 'margin',
    title: '占用保证金',
    width: 100,
    sortable: true,
    cellRenderer: render2Thousands,
  },
  { key: 'assetType', title: '类型', width: 100, sortable: true, cellRenderer: renderAccountType },
  { key: 'orgName', title: '机构', width: 100, sortable: true, cellRenderer: render2Thousands },
  {
    key: 'brokerName',
    title: '经纪商',
    width: 100,
    sortable: true,
    cellRenderer: render2Thousands,
  },
];

// 行操作
const rowActions: RowAction<LegacyAccountInfo>[] = [
  {
    label: '编辑',
    icon: 'edit',
    onClick: row => {
      editAccount(row);
    },
  },
  {
    label: '操作',
    icon: 'monitor',
    onClick: row => {
      console.log('to operate account', row);
    },
    nesteds: [
      {
        label: '费用设置',
        icon: 'setting',
        onClick: row => {
          const { accountId, accountName } = row;
          dialogFee.visible = true;
          nextTick(() => {
            $feeForm.value!.reset(accountId, accountName);
          });
        },
      },
      {
        label: '权益维护',
        icon: 'setting',
        onClick: row => {
          const {
            id,
            preBalance,
            available,
            frozenMargin,
            loanBuyBalance,
            loanSellBalance,
            loanSellQuota,
          } = row;
          const equity_info: AccountEquityInfo = {
            account_id: id,
            pre_balance: preBalance,
            available: available,
            frozen: frozenMargin,
            buy_balance: loanBuyBalance,
            sell_balance: loanSellBalance,
            sell_quota: loanSellQuota,
          };

          dialogEquity.visible = true;
          nextTick(() => {
            $equityForm.value!.reset(equity_info);
          });
        },
      },
      {
        label: '风控设置（暂未支持）',
        icon: 'setting',
        onClick: row => {
          ElMessage.error('功能尚未实现');
          console.log(Utils.deepClone(row));
        },
      },
      {
        label: '可用资金检查（暂未支持）',
        icon: 'setting',
        onClick: row => {
          ElMessage.error('功能尚未实现');
          console.log(Utils.deepClone(row));
        },
      },
      {
        label: '初始化',
        icon: 'setting',
        onClick: row => {
          takeAction(row, '初始化', repoInstance.InitAccount);
        },
      },
      {
        label: '清算',
        icon: 'setting',
        onClick: row => {
          takeAction(row, '清算', repoInstance.SettleAccount);
        },
      },
      {
        label: '比对',
        icon: 'setting',
        onClick: row => {
          takeAction(row, '比对', repoInstance.CompareAccount);
        },
      },
      {
        label: '覆盖',
        icon: 'setting',
        onClick: row => {
          takeAction(row, '覆盖', repoInstance.OverwriteAccount);
        },
      },
      {
        label: '资金覆盖',
        icon: 'setting',
        onClick: row => {
          takeAction(row, '资金覆盖', repoInstance.OverwriteAccountFinance);
        },
      },
      {
        label: '删除',
        icon: 'setting',
        type: 'danger',
        onClick: row => {
          deleteRow(row);
        },
      },
    ],
  },
];

async function takeAction(
  row: LegacyAccountInfo,
  action: string,
  method: (account_id: string) => Promise<any>,
) {
  const choice = await ElMessageBox.confirm(
    `对账号 "${row.accountName}" 进行 "${action}" 吗？`,
    '确认操作',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  );
  if (choice === 'confirm') {
    const { errorCode, errorMsg } = await method.call(repoInstance, row.id);
    if (errorCode === 0) {
      ElMessage.success('操作成功');
    } else {
      ElMessage.error(errorMsg || '操作失败');
    }
  }
}

const tableCfgParams = inject(TABLE_COLUMN_SELECT_KEY, reactive(getEmptyTableColumnConfig()));

function renderNameCol(params: CellRenderParam) {
  const { accountName, connectionStatus } = params.rowData;
  return (
    <div class="name-cell">
      <div class={[connectionStatus ? 'bg-green' : 'bg-red', 'status', 'toe']}></div>
      <div class="name toe">{accountName}</div>
    </div>
  );
}

function render2Thousands(params: CellRenderParam) {
  return <span>{Formatter.thousands(params.cellData as number)}</span>;
}

function renderPercentage(params: CellRenderParam) {
  return <span>{((params.cellData as number) * 100).toFixed(2)}%</span>;
}

function renderAccountType(params: CellRenderParam) {
  return <span>{Formatter.renderLabel(params.cellData as number, AssetTypes)}</span>;
}

const records = ref<LegacyAccountInfo[]>([]);

const $basicForm = useTemplateRef('basicForm');
const dialogEdit = reactive({
  visible: false,
  title: '',
});

function configColumn() {
  Object.assign(tableCfgParams, {
    name: '账号管理',
    columns: columns.map(x => ({ title: x!.title || '', datakey: x!.key })),
    selected: [],
    callback: (selected: string[]) => {
      console.log('selected columns', selected);
    },
  });
}

function createAccount() {
  editAccount(null);
}

function editAccount(target: LegacyAccountInfo | null) {
  dialogEdit.visible = true;
  dialogEdit.title = target ? '编辑账号' : '创建账号';
  nextTick(() => {
    $basicForm.value!.reset(target);
  });
}

function cancelEditBasic() {
  dialogEdit.visible = false;
}

const $equityForm = useTemplateRef('equityForm');
const dialogEquity = reactive({
  visible: false,
  title: '',
});

function handleEquitySaved(item: AccountEquityInfo) {
  dialogEquity.visible = false;
  console.log('handleEquitySaved', item);
}

function handleEquityCanceled(item: AccountEquityInfo) {
  dialogEquity.visible = false;
  console.log('handleEquityCanceled', item);
}

const $feeForm = useTemplateRef('feeForm');
const dialogFee = reactive({
  visible: false,
  title: '',
});

function handleFeeSaved() {
  dialogFee.visible = false;
}

function handleFeeCanceled() {
  dialogFee.visible = false;
}

async function basicSaved(row: LegacyAccountInfo) {
  const isModify = !!row.id;
  dialogEdit.visible = false;

  if (isModify) {
    const matched = records.value.find(item => item.id === row.id);
    if (matched) {
      Object.assign(matched, row);
    }
  } else {
    request();
  }
}

async function deleteRow(row: LegacyAccountInfo) {
  const choice = await ElMessageBox.confirm(
    `确定要删除账号 "${row.accountName}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  );
  if (choice === 'confirm') {
    const { errorCode, errorMsg } = await repoInstance.DeleteAccount(row.id);
    if (errorCode === 0) {
      ElMessage.success('删除成功');
      Utils.remove(records.value, x => x.id == row.id);
    } else {
      ElMessage.error(errorMsg || '删除失败');
    }
  }
}

async function request() {
  records.value = (await repoInstance.QueryAccounts()).data || [];
}

onMounted(() => {
  request();
});
</script>

<template>
  <div class="account-list-view product-guide-slide">
    <VirtualizedTable
      ref="tableRef"
      search-placeholder="搜索账号"
      :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
      :columns="columns"
      :data="records"
      :row-actions="rowActions"
      :row-action-width="170"
      select
      fixed
    >
      <template #actions>
        <div class="actions" flex aic>
          <el-button link size="small" class="typical-text-button" @click="configColumn">
            <i class="iconfont icon-setting"></i>
            <span>列配置</span>
          </el-button>
          <el-button link size="small" class="typical-text-button">
            <i class="iconfont icon-download"></i>
            <span>下载</span>
          </el-button>
          <el-button type="primary" @click="createAccount">
            <i class="iconfont icon-add-new" mr-5></i>
            <span>新建账号</span>
          </el-button>
        </div>
      </template>
    </VirtualizedTable>
    <el-dialog
      v-model="dialogEdit.visible"
      class="typical-dialog"
      width="624px"
      :title="dialogEdit.title"
      draggable
    >
      <AccountBasicInfoForm
        ref="basicForm"
        @save="basicSaved"
        @cancel="cancelEditBasic"
      ></AccountBasicInfoForm>
    </el-dialog>
    <el-dialog
      v-model="dialogEquity.visible"
      class="typical-dialog"
      width="624px"
      title="权益维护"
      draggable
    >
      <AccountEquityForm
        ref="equityForm"
        @cancel="handleEquityCanceled"
        @save="handleEquitySaved"
      ></AccountEquityForm>
    </el-dialog>
    <el-dialog
      v-model="dialogFee.visible"
      class="typical-dialog"
      width="624px"
      title="费用设置"
      draggable
    >
      <AccountFeeForm
        ref="feeForm"
        @cancel="handleFeeCanceled"
        @save="handleFeeSaved"
      ></AccountFeeForm>
    </el-dialog>
  </div>
</template>

<style scoped>
.account-list-view {
  height: 100%;
  width: 100%;
  :deep() {
    .name-cell {
      width: 100%;
      display: flex;
      align-items: center;
      gap: 6px;
    }
    .status {
      position: relative;
      top: 2px;
      width: 8px;
      height: 8px;
      border-radius: 4px;
    }
    .name {
      flex: 1 1 100px;
    }
  }
}
</style>
