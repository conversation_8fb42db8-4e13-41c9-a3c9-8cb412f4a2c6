<script setup lang="ts">
import { computed, defineAsyncComponent, provide, reactive, watch } from 'vue';
import { getEmptyTableColumnConfig, type TableColumnConfigParam } from '@/types';
import TableColumnConfig from '../common/TableColumnConfig.vue';
import { TABLE_COLUMN_SELECT_KEY } from '@/keys';
import { Utils } from '@/script';
import { type MomMenuTree } from '../../../../xtrade-sdk/dist';

const { activeMenu, include, menus } = defineProps<{
  activeMenu: MomMenuTree | null;
  include: string[];
  menus: MomMenuTree[];
}>();

const components: Record<string, unknown> = {};
const componentIns = computed(() => {
  if (!activeMenu) {
    return null;
  }
  return components[activeMenu.menuRoute!];
});

watch(
  () => menus.length,
  () => {
    loadComponent();
  },
  { immediate: true },
);

function loadComponent() {
  const processMenu = (menu: MomMenuTree) => {
    if (menu.menuRoute) {
      components[menu.menuRoute] = defineAsyncComponent(
        () => import(`../../views/HomeView/${menu.menuRoute}.vue`),
      );
    }
    menu.children?.forEach(processMenu);
  };
  menus.forEach(processMenu);
}

const tableCfgDialog = reactive({ visible: false, config: getEmptyTableColumnConfig() });
const tableCfgParams = reactive<TableColumnConfigParam>(getEmptyTableColumnConfig());
provide(TABLE_COLUMN_SELECT_KEY, tableCfgParams);
watch(() => tableCfgParams, openTableCfgDialog, { deep: true });

function openTableCfgDialog() {
  const cloned = Utils.deepClone(tableCfgParams);
  console.log('openTableCfgDialog', cloned);
  tableCfgDialog.config = Utils.deepClone(cloned);
  tableCfgDialog.visible = true;
}

function cfgChanged() {
  console.log('cfgChanged');
  tableCfgDialog.visible = false;
}

function cfgCancel() {
  console.log('cfgCancel');
  tableCfgDialog.visible = false;
}
</script>

<template>
  <div class="home-content-view">
    <KeepAlive :max="10" :include="include">
      <component :is="componentIns"></component>
    </KeepAlive>
    <TableColumnConfig
      :visible="tableCfgDialog.visible"
      :config="tableCfgDialog.config"
      @change="cfgChanged"
      @canceled="cfgCancel"
    ></TableColumnConfig>
  </div>
</template>

<style>
.home-content-view {
  width: 100%;
  height: 100%;

  > * {
    width: 100%;
    height: 100%;
  }
}
</style>
