<template>
  <div class="form-container product-guide-slide" pr-10 overflow-y-auto overflow-x-hidden>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      class="typical-form"
      label-position="left"
      label-width="220px"
      w-full
      mt-10
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="产品名称" prop="fundName">
            <el-input v-model.trim="formData.fundName" placeholder="产品名称" clearable>
              <template #prefix>
                <i class="iconfont icon-consist"></i>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="策略" prop="strategyType">
            <el-select
              v-model.trim="formData.strategyType"
              placeholder="请选择策略"
              filterable
              clearable
            >
              <el-option
                v-for="(item, idx) in StrategyTypes"
                :key="idx"
                :label="item.label"
                :value="item.value"
              />
              <template #prefix>
                <i class="iconfont icon-strategy"></i>
              </template>
            </el-select>
          </el-form-item>
          <el-form-item label="基金性质（后端暂无该字段）" prop="fundCategory">
            <el-radio-group
              v-model.trim="(formData as any).fundCategory"
              class="typical-radio-group"
              w-full
            >
              <el-radio v-for="(item, idx) in FundCategories" :key="idx" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="成立日" prop="establishedDay">
            <el-date-picker
              v-model.trim="formData.establishedDay"
              type="date"
              placeholder="选择日期"
              format="YYYY-MM-DD"
              value-format="YYYYMMDD"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="基金经理" prop="fundManager">
            <el-input v-model.trim="formData.fundManager" placeholder="基金经理名字" clearable>
              <template #prefix>
                <i class="iconfont icon-user"></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备案号" prop="amacCode">
            <el-input v-model.trim="formData.amacCode" clearable>
              <template #prefix>
                <i fs-18 class="iconfont icon-document-code"></i>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="基准" prop="basisReference">
            <el-select
              v-model.trim="formData.basisReference"
              placeholder="请选择基准"
              filterable
              clearable
            >
              <el-option
                v-for="(item, idx) in IndexBenchmarks"
                :key="idx"
                :label="item.label"
                :value="item.value"
              />
              <template #prefix>
                <i class="iconfont icon-riseup"></i>
              </template>
            </el-select>
          </el-form-item>
          <el-form-item label="基金类型" prop="fundType">
            <el-radio-group v-model.trim="formData.fundType" class="typical-radio-group" w-full>
              <el-radio v-for="(item, idx) in FundTypes" :key="idx" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="产品状态" prop="closedFlag">
            <el-radio-group v-model.trim="formData.closedFlag" class="typical-radio-group" w-full>
              <el-radio v-for="(item, idx) in FundStatuses" :key="idx" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="管理机构" prop="fundOrganization">
            <el-select
              v-model.trim="formData.fundOrganization"
              placeholder="请选择管理机构"
              filterable
              clearable
            >
              <el-option
                v-for="(item, idx) in orgs"
                :key="idx"
                :label="item.orgName"
                :value="item.id"
              />
              <template #prefix>
                <i class="iconfont icon-building"></i>
              </template>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注（后端暂无该字段）" prop="remark">
            <el-input v-model="(formData as any).remark" placeholder="备注信息" clearable>
              <template #prefix>
                <i class="iconfont icon-message"></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
  <div class="typical-dialog-footer" flex jcc gap-16 pt-16 pb-4>
    <el-button @click="cancel" w-200>取消</el-button>
    <el-button type="primary" @click="check" w-220>确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, shallowRef, useTemplateRef, watch } from 'vue';
import { type ProductInfo } from '@/types';
import { Misc, Utils } from '@/script';
import { Repos, type MomOrganization } from '../../../../xtrade-sdk/dist';
import { ElMessage } from 'element-plus';

import {
  StrategyTypes,
  FundTypes,
  FundCategories,
  FundStatuses,
  IndexBenchmarks,
} from '@/enum/product';

const repoInstance = new Repos.AdminRepo();
const repoGovInstance = new Repos.GovernanceRepo();
const contextProduct = defineModel<ProductInfo | null>({});
const formRef = useTemplateRef('formRef');
const formData = ref<ProductInfo>({} as any);

const rules = {
  fundName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
  fundOrganization: [{ required: true, message: '请选择管理机构', trigger: 'blur' }],
};

watch(
  () => contextProduct.value,
  newValue => {
    formData.value = newValue ? Utils.deepClone(newValue) : ({} as any);
  },
  { immediate: true },
);

const emitter = defineEmits<{
  cancel: [];
  save: [data: ProductInfo];
}>();

const cancel = () => {
  emitter('cancel');
  formRef.value?.clearValidate();
};

const check = () => {
  formRef.value?.validate(valid => {
    if (valid) {
      save();
    }
  });
};

async function save() {
  const isModify = !!formData.value.id;
  const behavior = isModify ? '修改' : '创建';
  const row = Utils.deepClone(formData.value);

  // 补充必要的字段

  if (Utils.isNone(row.orgId)) {
    // 补充必要的字段
    const usr = Misc.getUser()!;
    row.orgId = usr.orgId;
    row.orgName = usr.orgName;
  }

  const { errorCode, errorMsg } = isModify
    ? await repoGovInstance.UpdateProduct(row)
    : await repoGovInstance.CreateProduct(row);

  if (errorCode === 0) {
    ElMessage.success(`${behavior}成功`);
    emitter('save', row);
  } else {
    ElMessage.error(errorMsg || `${behavior}失败`);
  }
}

const orgs = shallowRef<MomOrganization[]>([]);

async function requestOrgs() {
  orgs.value = (await repoInstance.QueryOrgs()).data || [];
}

onMounted(() => {
  requestOrgs();
});
</script>

<style scoped></style>
