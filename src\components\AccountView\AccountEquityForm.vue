<template>
  <div class="form-container" overflow-y-auto overflow-x-hidden>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      class="typical-form"
      label-position="left"
      label-width="130px"
      w-full
      mt-10
    >
      <div class="half-col">
        <el-form-item label="昨日权益" prop="pre_balance">
          <el-input-number
            :controls="false"
            :precision="2"
            :step="0.01"
            v-model="formData.pre_balance"
            placeholder="昨日权益"
            clearable
          >
            <template #prefix>
              <i class="iconfont icon-block"></i>
            </template>
          </el-input-number>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="可用" prop="available">
          <el-input-number
            :controls="false"
            :precision="2"
            :step="0.01"
            v-model="formData.available"
            placeholder="可用资金"
            clearable
          >
            <template #prefix>
              <i class="iconfont icon-block"></i>
            </template>
          </el-input-number>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="冻结" prop="frozen">
          <el-input-number
            :controls="false"
            :precision="2"
            :step="0.01"
            v-model="formData.frozen"
            placeholder="冻结"
            clearable
          >
            <template #prefix>
              <i class="iconfont icon-building"></i>
            </template>
          </el-input-number>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="融资买入金额" prop="buy_balance">
          <el-input-number
            :controls="false"
            :precision="2"
            :step="0.01"
            v-model="formData.buy_balance"
            placeholder="融资买入金额"
            clearable
          >
            <template #prefix>
              <i class="iconfont icon-document-code"></i>
            </template>
          </el-input-number>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="融资卖出金额" prop="sell_balance">
          <el-input-number
            :controls="false"
            :precision="2"
            :step="0.01"
            v-model="formData.sell_balance"
            placeholder="融资卖出金额"
            clearable
          >
            <template #prefix>
              <i class="iconfont icon-document-code"></i>
            </template>
          </el-input-number>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="可用融券卖出金额" prop="sell_quota">
          <el-input-number
            :controls="false"
            :precision="2"
            :step="0.01"
            v-model="formData.sell_quota"
            placeholder="可用融券卖出金额"
            clearable
          >
            <template #prefix>
              <i class="iconfont icon-block"></i>
            </template>
          </el-input-number>
        </el-form-item>
      </div>
    </el-form>
  </div>
  <div class="typical-dialog-footer" flex jcc gap-16 pt-16 pb-4>
    <el-button @click="cancel" w-200>取消</el-button>
    <el-button type="primary" @click="check" w-220>确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, useTemplateRef } from 'vue';
import { Utils } from '@/script';
import { ElMessage } from 'element-plus';
import { Repos, type AccountEquityInfo } from '../../../../xtrade-sdk/dist';

const repoInstance = new Repos.GovernanceRepo();
const formRef = useTemplateRef('formRef');
const formData = ref<AccountEquityInfo>({} as any);

const rules = {
  pre_balance: [{ required: true, message: '请输入昨日权益', trigger: 'blur' }],
  available: [{ required: true, message: '请输入可用资金', trigger: 'blur' }],
  frozen: [{ required: true, message: '请输入冻结', trigger: 'blur' }],
  buy_balance: [{ required: true, message: '请输入融资买入金额', trigger: 'blur' }],
  sell_balance: [{ required: true, message: '请输入融资卖出金额', trigger: 'blur' }],
};

const emitter = defineEmits<{
  cancel: [data: AccountEquityInfo];
  save: [data: AccountEquityInfo];
}>();

const cancel = () => {
  const obj = formData.value!;
  emitter('cancel', Utils.deepClone(obj));
  formRef.value?.clearValidate();
};

const check = () => {
  formRef.value?.validate(valid => {
    if (valid) {
      save();
    }
  });
};

async function save() {
  const obj = formData.value!;
  const cloned = Utils.deepClone(obj);
  const { errorCode, errorMsg } = await repoInstance.SetAccountEquity(cloned);
  if (errorCode === 0) {
    ElMessage.success('操作成功');
    emitter('save', cloned);
  } else {
    ElMessage.error(errorMsg || '操作失败');
  }
}

function reset(context: AccountEquityInfo) {
  formData.value = Utils.deepClone(context);
}

defineExpose({
  reset,
});

onMounted(() => {
  //
});
</script>

<style scoped>
.form-container {
  .half-col {
    float: left;
    width: 50%;
  }
  :deep() {
    .el-form-item {
      width: 95%;
      margin-right: 10px !important;
    }

    .el-form-item__label {
      position: relative;
    }

    .el-input-number {
      width: 100%;
    }
  }
}
</style>
