<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import TerminalDialog from './TerminalDialog.vue';
import { onMounted, shallowRef, useTemplateRef } from 'vue';
import { TableV2SortOrder, ElMessage, ElMessageBox } from 'element-plus';
import type { ColumnDefinition, RowAction } from '@/types';
import { AdminService } from '@/api';
import { Formatter } from '@/script';
import { type MomTerminal, TerminalType } from '../../../../xtrade-sdk/dist';

// 基础列定义
const columns: ColumnDefinition<MomTerminal> = [
  // { key: 'id', title: '终端ID', width: 200, sortable: true },
  { key: 'terminalName', title: '终端名称', width: 200, sortable: true },
  { key: 'description', title: '描述信息', width: 250, sortable: true },
  {
    key: 'status',
    title: '状态',
    width: 200,
    sortable: true,
    cellRenderer: ({ rowData }: { rowData: MomTerminal }) => {
      return (
        <el-switch
          modelValue={rowData.status}
          active-value={1}
          inactive-value={0}
          before-change={() => beforeChange(rowData)}
        />
      );
    },
  },
  {
    key: 'interfaceType',
    title: '接口类型',
    width: 200,
    sortable: true,
    cellRenderer: formatInterfaceType,
  },
  {
    key: 'createTime',
    title: '创建时间',
    width: 200,
    sortable: true,
    cellRenderer: formatDateTime,
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 200,
    sortable: true,
    cellRenderer: formatDateTime,
  },
];

// 行操作
const rowActions: RowAction<MomTerminal>[] = [
  {
    label: '编辑',
    icon: 'edit',
    onClick: row => {
      editRow(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    onClick: row => {
      deleteRow(row);
    },
  },
];

const TerminalTypes = Object.values(TerminalType);
const records = shallowRef<MomTerminal[]>([]);
const tableRef = useTemplateRef('tableRef');

// 对话框相关
const dialogVisible = shallowRef(false);
const editingTerminal = shallowRef<MomTerminal | undefined>();

function formatDateTime(params: any) {
  return <span>{Formatter.formatDateTime(params.cellData)}</span>;
}

// 格式化接口类型
function formatInterfaceType({ cellData }: { cellData: number }) {
  return <span>{TerminalTypes.find(x => x.Value == cellData)?.Label || cellData}</span>;
}

const beforeChange = async (rowData: MomTerminal) => {
  const { errorCode, errorMsg } = await AdminService.updateTerminal({
    ...rowData,
    status: rowData.status == 1 ? 0 : 1,
  });
  if (errorCode === 0) {
    rowData.status = rowData.status == 1 ? 0 : 1;
    return true;
  } else {
    ElMessage.error(errorMsg || '操作失败');
    return false;
  }
};

// 新建终端
const handleCreate = () => {
  editingTerminal.value = undefined;
  dialogVisible.value = true;
};

// 编辑终端
function editRow(row: MomTerminal) {
  editingTerminal.value = row;
  dialogVisible.value = true;
}

// 删除终端
function deleteRow(row: MomTerminal) {
  ElMessageBox.confirm(`确定要删除终端"${row.terminalName}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { errorCode, errorMsg } = await AdminService.deleteTerminal(row.id);
      if (errorCode === 0) {
        ElMessage.success('删除成功');
        await request();
      } else {
        ElMessage.error(errorMsg || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  });
}

// 对话框成功回调
const handleDialogSuccess = async () => {
  await request();
};

async function request() {
  try {
    records.value = await AdminService.getTerminals();
  } catch (error) {
    console.error('获取终端列表失败:', error);
    ElMessage.error('获取终端列表失败');
  }
}

onMounted(() => {
  request();
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
    :columns="columns"
    :data="records"
    :row-actions="rowActions"
    :row-action-width="170"
    select
    fixed
  >
    <template #actions>
      <div class="actions" flex aic>
        <!-- <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-setting"></i>
          <span>列配置</span>
        </el-button>
        <el-button link size="small" class="typical-text-button">
          <i class="iconfont icon-download"></i>
          <span>下载</span>
        </el-button> -->
        <el-button type="primary" @click="handleCreate">
          <i class="iconfont icon-add-new" mr-5></i>
          <span>新建交易终端</span>
        </el-button>
      </div>
    </template>
  </VirtualizedTable>

  <!-- 终端编辑对话框 -->
  <TerminalDialog
    v-model="dialogVisible"
    :terminal="editingTerminal"
    @success="handleDialogSuccess"
  />
</template>

<style scoped></style>
