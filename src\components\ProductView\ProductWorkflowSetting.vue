<script setup lang="tsx">
import { nextTick, onMounted, ref } from 'vue';
import { type WorkflowConfig, WorkflowType } from '../../../../xtrade-sdk/dist';
import { Formatter, Utils } from '@/script';
import type { InputInstance } from 'element-plus';

const records = ref<WorkflowConfig[]>([]);
const editingRecord = ref<WorkflowConfig | null>(null);
const editingIndex = ref<number | null>(null);

// 模拟角色和账号数据
const roleOptions = ref([
  { roleId: 1, roleName: '投资顾问' },
  { roleId: 2, roleName: '投顾' },
  { roleId: 3, roleName: '交易员' },
  { roleId: 4, roleName: '风控员' },
  { roleId: 5, roleName: '风控主管' },
]);

const accountOptions = ref([
  { accountId: '1', accountName: '账号A' },
  { accountId: '2', accountName: '账号B' },
  { accountId: '3', accountName: '账号C' },
  { accountId: '4', accountName: '账号D' },
]);

function renderType(type: number) {
  return Formatter.renderLabel(type, Object.values(WorkflowType));
}

const inputValue = ref('');
const dynamicTags = ref(['User1', 'User2', 'User3']);
const inputVisible = ref(false);
const InputRef = ref<InputInstance>();

const handleClose = (tag: string) => {
  dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1);
};

const showInput = () => {
  inputVisible.value = true;
  nextTick(() => {
    InputRef.value!.input!.focus();
  });
};

const handleInputConfirm = () => {
  if (inputValue.value) {
    dynamicTags.value.push(inputValue.value);
  }
  inputVisible.value = false;
  inputValue.value = '';
};

// 开始编辑记录
const startEditing = (record: WorkflowConfig, index: number) => {
  editingRecord.value = Utils.deepClone(record);
  editingIndex.value = index;
};

// 保存编辑
const saveEditing = () => {
  if (editingIndex.value !== null && editingRecord.value) {
    records.value[editingIndex.value] = editingRecord.value;
  }
  cancelEditing();
};

// 取消编辑
const cancelEditing = () => {
  editingRecord.value = null;
  editingIndex.value = null;
};

// 处理步骤选择变化
const handleStepChange = (val: any[]) => {
  if (editingRecord.value) {
    // 根据选中的角色ID构建新的步骤数组
    editingRecord.value.steps = val.map(roleId => {
      const role = roleOptions.value.find(r => r.roleId === roleId);
      return {
        roleId: roleId,
        roleName: role ? role.roleName : '',
        members: [],
      };
    });
  }
};

// 处理账号选择变化
const handleAccountChange = (val: any[]) => {
  if (editingRecord.value) {
    // 根据选中的账号ID构建新的账号数组
    editingRecord.value.accounts = val.map(accountId => {
      const account = accountOptions.value.find(a => a.accountId === accountId);
      return {
        accountId: accountId,
        accountName: account ? account.accountName : '',
      };
    });
  }
};

async function request() {
  records.value = [
    {
      id: 1,
      name: '交易流程0815',
      type: WorkflowType.Trading.Value,
      steps: [
        { roleId: 1, roleName: '投资顾问', members: [] },
        { roleId: 2, roleName: '投顾', members: [] },
        { roleId: 3, roleName: '交易员', members: [] },
      ],
      accounts: [
        { accountId: '1', accountName: '账号A' },
        { accountId: '2', accountName: '账号B' },
        { accountId: '3', accountName: '账号C' },
      ],
    },
    {
      id: 2,
      name: '风控流程0815',
      type: WorkflowType.Approving.Value,
      steps: [
        { roleId: 4, roleName: '风控员', members: [] },
        { roleId: 5, roleName: '风控主管', members: [] },
      ],
      accounts: [
        { accountId: '1', accountName: '账号A' },
        { accountId: '2', accountName: '账号B' },
        { accountId: '3', accountName: '账号C' },
      ],
    },
  ];
}

onMounted(() => {
  request();
});
</script>

<template>
  <div class="user-and-flow-config">
    <div mt-12 lh-28 fs-20 fw-400 color="[--g-text-color-5]">流程配置</div>
    <div class="each-row row-title">
      <div class="data-col flexible" w-80>流程名称</div>
      <div class="data-col" w-150>类型</div>
      <div class="data-col flexible" w-150>流程节点</div>
      <div class="data-col flexible" w-120>绑定账号</div>
      <div class="oper-col">操作</div>
    </div>
    <div class="data-rows">
      <div class="each-row row-data" v-for="(record, idx) in records" :key="idx">
        <template v-if="editingIndex === idx && editingRecord">
          <!-- 编辑状态 -->
          <div class="data-col flexible" w-80>
            <el-input v-model.trim="editingRecord.name" />
          </div>
          <div class="data-col" w-150>
            <el-select v-model="editingRecord.type">
              <el-option
                v-for="type in Object.values(WorkflowType)"
                :key="type.Value"
                :label="type.Label"
                :value="type.Value"
              />
            </el-select>
          </div>
          <div class="data-col flexible" w-150 flex gap-5>
            <el-select
              v-model="editingRecord.steps"
              multiple
              collapse-tags
              collapse-tags-tooltip
              @change="handleStepChange"
              style="width: 100%"
            >
              <el-option
                v-for="role in roleOptions"
                :key="role.roleId"
                :label="role.roleName"
                :value="role.roleId"
              />
            </el-select>
          </div>
          <div class="data-col flexible" w-120 flex gap-5>
            <el-select
              v-model="editingRecord.accounts"
              multiple
              collapse-tags
              collapse-tags-tooltip
              @change="handleAccountChange"
              style="width: 100%"
            >
              <el-option
                v-for="account in accountOptions"
                :key="account.accountId"
                :label="account.accountName"
                :value="account.accountId"
              />
            </el-select>
          </div>
          <div class="oper-col">
            <el-button link @click="saveEditing">
              <i class="iconfont icon-save" />
              <span pl-5>保存</span>
            </el-button>
            <el-button link @click="cancelEditing">
              <i class="iconfont icon-close" />
              <span pl-5>取消</span>
            </el-button>
          </div>
        </template>
        <template v-else>
          <!-- 显示状态 -->
          <div class="data-col flexible" w-80 toe>{{ record.name }}</div>
          <div class="data-col" w-100>{{ renderType(record.type) }}</div>
          <div class="data-col flexible" w-150 flex gap-5>
            <template v-for="(step, idx2) in record.steps" :key="idx2">
              <span>{{ step.roleName }}</span>
              <i
                v-if="idx2 !== record.steps.length - 1"
                class="iconfont icon-arrow-right-circle"
              ></i>
            </template>
          </div>
          <div class="data-col flexible" w-120 flex gap-5>
            <template v-for="(acnt, idx2) in record.accounts" :key="idx2">
              <span>{{ acnt.accountName }}</span>
              <span v-if="idx2 !== record.accounts.length - 1">、</span>
            </template>
          </div>
          <div class="oper-col">
            <el-button link @click="startEditing(record, idx)">
              <i class="iconfont icon-setting" />
              <span pl-5>设置</span>
            </el-button>
            <el-button link>
              <i class="iconfont icon-remove" />
              <span pl-5>删除</span>
            </el-button>
          </div>
        </template>
      </div>
    </div>
    <div class="row-add hopc-8" h-40 flex aic jcc gap-12>
      <i class="iconfont icon-add"></i>
      <span>新建流程</span>
    </div>
    <div mt-12 mb-12 lh-28 fs-20 fw-400 color="[--g-text-color-5]">人员配置</div>
    <div class="row-add hopc-8" h-40 flex aic gap-20>
      <div>查询员</div>
      <el-tag
        v-for="tag in dynamicTags"
        :key="tag"
        closable
        :disable-transitions="false"
        @close="handleClose(tag)"
      >
        {{ tag }}
      </el-tag>
      <el-input
        v-if="inputVisible"
        ref="InputRef"
        v-model="inputValue"
        style="width: 200px"
        @keyup.enter="handleInputConfirm"
        @blur="handleInputConfirm"
      />
      <el-button v-else class="button-new-tag" @click="showInput" style="height: 28px">
        + 搜索添加人员
      </el-button>
    </div>
  </div>
</template>

<style scoped>
.user-and-flow-config {
  margin-bottom: 20px;

  .config-title {
    font-weight: bold;
    margin-bottom: 15px;
  }

  .each-row {
    height: 40px;
    line-height: 40px;
    display: flex;
    padding: 0 25px;

    .data-col {
      padding-right: 20px;
      &.flexible {
        flex-grow: 1;
        flex-shrink: 1;
        flex-wrap: nowrap;
      }
    }

    .oper-col {
      width: 140px;
    }

    &.row-title {
      color: var(--g-text-color-1);
      font-weight: 400;
    }

    &.row-data {
      color: var(--g-text-color-2);
      font-weight: 400;
    }
  }

  .data-rows {
    .row-data {
      &:nth-child(odd) {
        background-color: var(--g-block-bg-2);
      }
    }
  }

  .row-add {
    background-color: var(--g-block-bg-2);
    cursor: default;
    padding: 0 25px;

    :deep() {
      .el-tag,
      .el-tag.el-tag--primary {
        background-color: var(--g-block-bg-1) !important;
      }
    }
  }
}
</style>
