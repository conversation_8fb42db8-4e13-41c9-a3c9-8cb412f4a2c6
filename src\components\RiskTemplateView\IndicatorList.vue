<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import { onMounted, ref, useTemplateRef } from 'vue';
import { TableV2SortOrder, ElSwitch, ElMessage } from 'element-plus';
import type { ColumnDefinition, RowAction } from '@/types';
import { RiskControlService } from '@/api';
import { Formatter, Utils } from '@/script';
import type { RiskIndicator, RiskRule } from '../../../../xtrade-sdk/dist';
import { deleteConfirm } from '@/script/interaction';

// 基础列定义
const columns: ColumnDefinition<RiskRule> = [
  {
    key: 'ruleName',
    title: '风控规则名称',
    width: 200,
    sortable: true,
  },
  {
    key: 'isActive',
    title: '是否启用',
    width: 100,
    sortable: true,
    cellRenderer: ({ rowData, cellData }) => {
      return <ElSwitch v-model={rowData.isActive}>{cellData ? '是' : '否'}</ElSwitch>;
    },
  },
  {
    key: 'beginTime',
    title: '运行时间',
    width: 300,
    sortable: true,
    cellRenderer: formatRunningTime,
  },
  {
    key: 'checkObject',
    title: '检查对象',
    width: 100,
    sortable: true,
    cellRenderer: formatObject,
  },
  {
    key: 'checkTime',
    title: '检查时机',
    width: 100,
    sortable: true,
  },
  {
    key: 'checkInterval',
    title: '间隔检查(秒)',
    width: 110,
    sortable: true,
  },
  {
    key: 'createTime',
    title: '创建时间',
    width: 100,
    sortable: true,
    cellRenderer: formatDateTime,
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 100,
    sortable: true,
    cellRenderer: formatDateTime,
  },
  {
    key: 'createUserId',
    title: '创建者用户ID',
    width: 120,
    sortable: true,
  },
  {
    key: 'orgId',
    title: '机构ID',
    width: 100,
    sortable: true,
  },
];

// 行操作
const rowActions: RowAction<RiskRule>[] = [
  {
    label: '编辑',
    icon: 'setting',
    type: 'text',
    onClick: row => {
      editRow(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    type: 'text',
    onClick: row => {
      deleteRow(row);
    },
  },
];

function editRow(row: RiskRule) {
  console.log('edit', row);
}

async function deleteRow(row: RiskRule) {
  const result = await deleteConfirm('删除风控项', `确认删除此风控项： ${row.ruleName}？`);

  if (result !== true) {
    return;
  }

  const resp = await RiskControlService.deleteRules([row.id]);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    Utils.remove(records.value, x => x.id == row.id);
  } else {
    ElMessage.error('风控规则删除失败：' + errorMsg);
  }
}

async function deleteRows() {
  const rows = tableRef.value?.selectedRows ?? [];

  if (rows.length === 0) {
    ElMessage.warning('请选择需要删除的行');
    return;
  }

  const result = await deleteConfirm('删除风控项', `确认删除勾选的风控项，数量 = ${rows.length}？`);

  if (result !== true) {
    return;
  }

  const ids = rows.map(x => x.id);
  const resp = await RiskControlService.deleteRules(ids);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    Utils.remove(records.value, x => ids.includes(x.id));
  } else {
    ElMessage.error('勾选风控规则，删除失败：' + errorMsg);
  }
}

const emitter = defineEmits<{
  add: [callback: (current: RiskIndicator, selecteds: RiskIndicator[]) => void];
}>();

function request2Add() {
  emitter('add', addCallback);
}

function addCallback(current: RiskIndicator, selecteds: RiskIndicator[]) {
  console.log('add callback', { current, selecteds });
  const item: RiskRule = {
    id: records.value.length + 1,
    templateId: 101,
    ruleName: current.indicatorName,
    indicatorId: 0,
    configuration: {
      maxOrderSize: 1000000,
      threshold: 0.95,
    },
    beginTime: 90000,
    endTime: 150000,
    beginDay: 20231001,
    endDay: 20231231,
    checkObject: 1,
    checkTime: 1,
    checkInterval: 60,
    isActive: true,
    createUserId: 1001,
    orgId: 201,
  };

  records.value.unshift(item);
}

const records = ref<RiskRule[]>([]);
const tableRef = useTemplateRef('tableRef');

function formatDateTime(params: any) {
  return <span>{Formatter.formatDateTime(params.cellData)}</span>;
}

function formatRunningTime(params: { rowData: RiskRule }) {
  function format(date: number | string, hms: number | string) {
    if (!date || !hms) {
      return 'NA';
    }

    date = date.toString();
    hms = hms.toString();

    while (hms.length < 6) {
      hms = '0' + hms;
    }

    const date_str = Formatter.formatDateTime(date, 'yyyy-MM-dd');
    const hms_str = hms.substring(0, 2) + ':' + hms.substring(2, 4) + ':' + hms.substring(4, 6);
    return date_str + ' ' + hms_str;
  }

  const { beginDay, beginTime, endDay, endTime } = params.rowData;
  return (
    <span>
      {format(beginDay, beginTime)} ~ {format(endDay, endTime)}
    </span>
  );
}

function formatObject(params: { rowData: RiskRule }) {
  const { checkObject } = params.rowData;
  const result = checkObject == 1 ? '指令' : checkObject == 2 ? '委托' : '指令+委托';
  return <span>{result}</span>;
}

async function request() {
  records.value = (await RiskControlService.getRules()) || [];
}

onMounted(() => {
  request();
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
    :columns="columns"
    :data="records"
    :row-actions="rowActions"
    :row-action-width="210"
    select
    fixed
  >
    <template #actions>
      <div class="actions" flex aic>
        <el-button type="primary" @click="request2Add">
          <span flex aic gap-6>
            <i class="iconfont icon-add"></i>
            <span>新增</span>
          </span>
        </el-button>
        <el-button type="default" @click="deleteRows">
          <span flex aic gap-6 thb2>
            <i class="iconfont icon-remove"></i>
            <span>删除</span>
          </span>
        </el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>
